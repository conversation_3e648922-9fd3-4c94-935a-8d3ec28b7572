import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {InventoryItem, InventoryItemWithRelations, Warehouse} from '../models';
import {
  CONTENT_TYPE,
  ModifiedRestService,
  restService,
  STATUS_CODE,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {service} from '@loopback/core';
import {InventoryItemService} from '../services';

const basePath = '/inventory-items';

export class InventoryItemController {
  constructor(
    @restService(InventoryItem)
    private readonly inventoryItemService: ModifiedRestService<InventoryItem>,
    @restService(Warehouse)
    private readonly warehouseService: ModifiedRestService<Warehouse>,
    @service(InventoryItemService)
    private readonly inventoryItemHelperService: InventoryItemService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateInventory]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'InventoryItem model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(InventoryItem)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(InventoryItem, {
            title: 'NewInventoryItem',
            exclude: ['id', 'sku'],
          }),
        },
      },
    })
    inventoryItem: Omit<InventoryItem, 'id, sku'>,
  ): Promise<InventoryItem> {
    return this.inventoryItemService.create(inventoryItem);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewInventory]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'InventoryItem model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(InventoryItem) where?: Where<InventoryItem>,
  ): Promise<Count> {
    return this.inventoryItemService.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewInventory]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of InventoryItem model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              ...getModelSchemaRef(InventoryItem).definitions.InventoryItem
                .properties,
              warehouse: getModelSchemaRef(Warehouse),
            },
          },
        },
      },
    },
  })
  async find(
    @param.header.string('x-origin') xOrigin: string,
    @param.header.string('Authorization') token?: string,
    @param.filter(InventoryItem) filter?: Filter<InventoryItem>,
  ): Promise<InventoryItemWithRelations[]> {
    filter = await this.inventoryItemHelperService.applySellerFilter(
      xOrigin,
      token,
      filter,
    );
    const inventoryItems = await this.inventoryItemService.find(filter);
    return this.inventoryItemHelperService.findWithWarehouse(inventoryItems);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewInventory]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'InventoryItem model instance with warehouse details',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'object',
          properties: {
            ...getModelSchemaRef(InventoryItem, {includeRelations: true})
              .definitions?.InventoryItem?.properties,
            warehouse: getModelSchemaRef(Warehouse),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(InventoryItem, {exclude: 'where'})
    filter?: FilterExcludingWhere<InventoryItem>,
  ): Promise<InventoryItem & {warehouse: Warehouse}> {
    const inventoryItem = await this.inventoryItemService.findById(id, filter);
    const warehouse = await this.warehouseService.findById(
      inventoryItem.warehouseId,
    );
    return {...inventoryItem, warehouse} as InventoryItem & {
      warehouse: Warehouse;
    };
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateInventory]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'InventoryItem PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(InventoryItem, {partial: true}),
        },
      },
    })
    inventoryItem: InventoryItem,
  ): Promise<void> {
    await this.inventoryItemService.updateById(id, inventoryItem);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteInventory]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'InventoryItem DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.inventoryItemService.deleteById(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewPlan]})
  @get(`${basePath}/available-stock/{productVariantId}`)
  @response(STATUS_CODE.OK, {
    description: 'Available stock for the given product variant',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'object',
          properties: {
            productVariantId: {type: 'string'},
            availableStock: {type: 'number'},
          },
        },
      },
    },
  })
  async getAvailableStock(
    @param.path.string('productVariantId') productVariantId: string,
  ): Promise<{availableStock: number}> {
    const inventories = await this.inventoryItemService.find({
      where: {
        productVariantId,
        stockOnHand: {gt: 0},
      },
      fields: {
        id: true,
        stockOnHand: true,
      },
    });

    const availableStock = inventories.reduce(
      (sum, inv) => sum + inv.stockOnHand,
      0,
    );

    return {availableStock};
  }
}
