import {inject, Provider} from '@loopback/core';
import {HttpErrors} from '@loopback/rest';
import {
  INotificationConfig,
  NotificationBindings,
} from 'loopback4-notifications';
import {ZeptoBindings} from '../keys';
import {ZeptoMessage, ZeptoNotification} from '../types';
import {SendMailClient, ZeptoMailMessage, ZeptoMailOptions} from 'zeptomail';

export class ZeptpMailProvider implements Provider<ZeptoNotification> {
  constructor(
    @inject(NotificationBindings.Config, {
      optional: true,
    })
    private readonly config?: INotificationConfig,
    @inject(ZeptoBindings.Config, {
      optional: true,
    })
    private readonly zeptoConfig?: ZeptoMailOptions,
  ) {
    if (this.zeptoConfig) {
      this.zeptoClient = new SendMailClient(this.zeptoConfig);
    } else {
      throw new HttpErrors.PreconditionFailed('Zepto Mail Config missing !');
    }
  }

  zeptoClient: SendMailClient;

  value() {
    return {
      publish: async (message: ZeptoMessage) => {
        const fromEmail =
          message.options?.fromEmail ?? this.config?.senderEmail;

        if (!fromEmail) {
          throw new HttpErrors.BadRequest(
            'Message sender not found in request',
          );
        }

        if (message.receiver.to.length === 0) {
          throw new HttpErrors.BadRequest(
            'Message receiver not found in request',
          );
        }
        if (!message.subject || !message.body) {
          throw new HttpErrors.BadRequest('Message data incomplete');
        }

        if (this.config?.sendToMultipleReceivers) {
          const receivers = message.receiver.to.map(receiver => ({
            // eslint-disable-next-line @typescript-eslint/naming-convention
            email_address: {
              address: receiver.id,
              name: receiver.name,
            },
          }));

          const emailReq: ZeptoMailMessage = {
            from: {address: fromEmail, name: 'Ecomdukes-noreply'},
            to: receivers,
            subject: message.subject ?? '',
            htmlbody: message.body,
          };

          await this.zeptoClient.sendMail(emailReq);
        } else {
          const publishes = message.receiver.to.map(receiver => {
            const emailReq: ZeptoMailMessage = {
              from: fromEmail ?? '',
              to: [
                // eslint-disable-next-line @typescript-eslint/naming-convention
                {email_address: {address: receiver.id, name: receiver.name}},
              ],
              subject: message.subject ?? '',
              htmlbody: message.body,
            };
            return this.zeptoClient.sendMail(emailReq);
          });

          await Promise.all(publishes);
        }
      },
    };
  }
}
