import { customizationSchema } from 'types/customisation';
import * as yup from 'yup';
import {
  facetsSchema,
  boxContentsSchema,
  detailsSchema,
  uniquenessSchema,
  suitabilitySchema,
  personalWorkSchema,
  returnPolicySchema,
  termsSchema,
  disclaimerSchema,
  specificationSchema
} from './shared-product';

export const productVariantSchema = yup.object({
  name: yup.string().required('Product name is required'),
  price: yup.number().required().min(1).label('Price'),
  priceWithoutTax: yup.number().required().min(1).label('Price'),
  mrp: yup
    .number()
    .transform((value, originalValue) => (originalValue === '' || originalValue === null ? NaN : Number(originalValue)))
    .required('MRP is required')
    .moreThan(yup.ref('priceWithoutTax'), 'MRP must be greater than price')
    .label('MRP'),

  enabled: yup.boolean().required('Enabled status is required'),
  assets: yup.array().of(yup.string().uuid('Invalid asset ID')),
  featuredAssetId: yup.string().uuid('Invalid featured asset ID').required('Featured Asset ID is required'),
  facets: facetsSchema,
  boxContents: boxContentsSchema.optional().default(undefined),
  details: detailsSchema.optional().default(undefined),
  uniqueness: uniquenessSchema.optional().default(undefined),
  suitability: suitabilitySchema.optional().default(undefined),
  personalWork: personalWorkSchema.optional().default(undefined),
  returnPolicy: returnPolicySchema.optional().default(undefined),
  terms: termsSchema.optional().default(undefined),
  disclaimer: disclaimerSchema.optional().default(undefined),
  specifications: specificationSchema.optional().default(undefined),
  customizations: yup.array().of(customizationSchema).optional().default(undefined)
});
