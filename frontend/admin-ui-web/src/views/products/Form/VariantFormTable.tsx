'use client';
import { Checkbox, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Paper } from '@mui/material';
import { FormikProps } from 'formik';
import { FC } from 'react';
import { ProductDto } from 'types/product-dto';

interface Props {
  formik: FormikProps<ProductDto>;
}

export const VariantFormTable: FC<Props> = ({ formik }) => {
  return (
    <TableContainer component={Paper}>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell padding="checkbox" />
            <TableCell>Variant Name</TableCell>
            <TableCell>SKU</TableCell>
            <TableCell>MRP</TableCell>
            <TableCell>Price</TableCell>
            <TableCell>Out of Stock Threshold</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {formik.values.variants.map((variant, idx) => {
            const priceGreaterThanMRP =
              Number(variant.price) > Number(variant.mrp) && variant.price !== undefined && variant.mrp !== undefined;

            // ✅ Determine row-wide error text for alignment
            const rowHasError =
              priceGreaterThanMRP || (!!formik.errors.variants?.[idx] && typeof formik.errors.variants?.[idx] === 'object');

            const getHelperText = (fieldError?: string | false) =>
              rowHasError
                ? fieldError || ' ' // keep same height when error
                : ' '; // reserve empty space when no error

            return (
              <TableRow key={variant.id || idx}>
                <TableCell padding="checkbox">
                  <Checkbox name={`variants[${idx}].enabled`} checked={variant.enabled} onChange={formik.handleChange} />
                </TableCell>
                <TableCell>
                  <TextField
                    fullWidth
                    name={`variants[${idx}].name`}
                    value={variant.name}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    size="small"
                    error={!!formik.touched.variants?.[idx]?.name && !!(formik.errors.variants?.[idx] as any)?.name}
                    helperText={getHelperText(formik.touched.variants?.[idx]?.name && (formik.errors.variants?.[idx] as any)?.name)}
                    FormHelperTextProps={{
                      sx: { fontSize: '0.7rem', lineHeight: 1.2, minHeight: '18px' }
                    }}
                    disabled
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    name={`variants[${idx}].sku`}
                    value={variant.sku || ''}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    fullWidth
                    size="small"
                    placeholder="SKU"
                    error={!!formik.touched.variants?.[idx]?.sku && !!(formik.errors.variants?.[idx] as any)?.sku}
                    helperText={getHelperText(formik.touched.variants?.[idx]?.sku && (formik.errors.variants?.[idx] as any)?.sku)}
                    FormHelperTextProps={{
                      sx: { fontSize: '0.7rem', lineHeight: 1.2, minHeight: '18px' }
                    }}
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    fullWidth
                    type="number"
                    name={`variants[${idx}].mrp`}
                    value={variant.mrp}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    size="small"
                    error={!!formik.touched.variants?.[idx]?.mrp && !!(formik.errors.variants?.[idx] as any)?.mrp}
                    helperText={getHelperText(formik.touched.variants?.[idx]?.mrp && (formik.errors.variants?.[idx] as any)?.mrp)}
                    FormHelperTextProps={{
                      sx: { fontSize: '0.7rem', lineHeight: 1.2, minHeight: '18px' }
                    }}
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    fullWidth
                    type="number"
                    name={`variants[${idx}].price`}
                    value={variant.price}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    size="small"
                    error={
                      priceGreaterThanMRP || (!!formik.touched.variants?.[idx]?.price && !!(formik.errors.variants?.[idx] as any)?.price)
                    }
                    helperText={getHelperText(
                      priceGreaterThanMRP
                        ? 'Price must be ≤ MRP'
                        : formik.touched.variants?.[idx]?.price && (formik.errors.variants?.[idx] as any)?.price
                    )}
                    FormHelperTextProps={{
                      sx: { fontSize: '0.69rem', lineHeight: 1.2, minHeight: '18px' }
                    }}
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    fullWidth
                    type="number"
                    name={`variants[${idx}].outOfStockThreshold`}
                    value={variant.outOfStockThreshold}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    size="small"
                    error={
                      !!formik.touched.variants?.[idx]?.outOfStockThreshold && !!(formik.errors.variants?.[idx] as any)?.outOfStockThreshold
                    }
                    helperText={getHelperText(
                      formik.touched.variants?.[idx]?.outOfStockThreshold && (formik.errors.variants?.[idx] as any)?.outOfStockThreshold
                    )}
                    FormHelperTextProps={{
                      sx: { fontSize: '0.7rem', lineHeight: 1.2, minHeight: '18px' }
                    }}
                  />
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
