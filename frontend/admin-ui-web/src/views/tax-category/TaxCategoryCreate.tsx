'use client';

import { useFormik } from 'formik';
import { Box, But<PERSON>, Card, CardContent, Grid, TextField, Typography } from '@mui/material';
import { useRouter } from 'next/navigation';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { useCreateTaxCategoryMutation, useUpdateTaxCategoryMutation } from 'redux/app/tax/taxCategoryApiSlice';
import { TaxCategory, TaxCategoryFormProps } from 'types/tax';
import { taxValidationSchema } from '../../../validations/tax';

function TaxCategoryCreate({ isEdit = false, taxCategoryId, initialValues, refetch }: TaxCategoryFormProps) {
  const router = useRouter();
  const [createTaxCategory] = useCreateTaxCategoryMutation();
  const [updateTaxCategory] = useUpdateTaxCategoryMutation();

  const handleFormSubmit = async (values: Partial<TaxCategory>) => {
    const payload: Partial<TaxCategory> = {
      name: values.name,
      hsnCode: values.hsnCode || undefined,
      description: values.description,
      taxRate: values.taxRate !== undefined ? Number(values.taxRate) : 0,
      isDefault: values.isDefault
    };

    if (isEdit && taxCategoryId) {
      await updateTaxCategory({ id: taxCategoryId, data: payload }).unwrap();
      openSnackbar({
        open: true,
        message: 'Tax Category updated successfully',
        variant: 'alert',
        alert: { color: 'success' }
      } as SnackbarProps);
      refetch?.();
      router.push('/settings/tax-category');
    } else {
      await createTaxCategory(payload).unwrap();
      openSnackbar({
        open: true,
        message: 'Tax Category created successfully',
        variant: 'alert',
        alert: { color: 'success' }
      } as SnackbarProps);
      refetch?.();
      router.push('/settings/tax-category');
    }
  };

  const formik = useFormik({
    initialValues: {
      name: initialValues?.name || '',
      description: initialValues?.description || '',
      hsnCode: initialValues?.hsnCode || undefined,
      taxRate: initialValues?.taxRate || 0,
      isDefault: initialValues?.isDefault ?? false
    },
    validationSchema: taxValidationSchema,
    onSubmit: handleFormSubmit
  });

  return (
    <Card sx={{ maxWidth: '100%', mx: 'auto', mt: 2, p: 2, boxShadow: 3 }}>
      <CardContent>
        <form onSubmit={formik.handleSubmit}>
          <Typography variant="h5" sx={{ mb: 2 }}>
            Create Tax Category
          </Typography>
          <Grid container spacing={2} sx={{ mt: 2 }}>
            <Grid item xs={6}>
              <Typography variant="h6">Name</Typography>
              <TextField
                fullWidth
                name="name"
                value={formik.values.name}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.name && Boolean(formik.errors.name)}
                helperText={formik.touched.name && formik.errors.name}
                margin="dense"
              />
            </Grid>

            <Grid item xs={6}>
              <Typography variant="h6">HSN Code</Typography>
              <TextField
                fullWidth
                name="hsnCode"
                value={formik.values.hsnCode}
                onChange={(e) => {
                  const onlyDigits = e.target.value.replace(/\D/g, '');
                  formik.setFieldValue('hsnCode', onlyDigits);
                }}
                onBlur={formik.handleBlur}
                error={formik.touched.hsnCode && Boolean(formik.errors.hsnCode)}
                helperText={formik.touched.hsnCode && formik.errors.hsnCode}
                margin="dense"
                inputProps={{
                  maxLength: 10
                }}
              />
            </Grid>
            {/* <Grid item xs={6}>
              <Typography variant="h6">Tax Rate (%)</Typography>
              <TextField
                fullWidth
                name="taxRate"
                value={formik.values.taxRate}
                onChange={(e) => {
                  const num = e.target.value === '' ? 0 : Number(e.target.value);
                  formik.setFieldValue('taxRate', num);
                }}
                onBlur={formik.handleBlur}
                error={!!formik.touched.taxRate && !!formik.errors.taxRate}
                helperText={formik.touched.taxRate && formik.errors.taxRate}
                margin="dense"
                inputProps={{
                  min: 0,
                  max: 100,
                  step: 0.1
                }}
              />
            </Grid> */}
            <Grid item xs={6}>
              <Typography variant="h6">Tax Rate (%)</Typography>
              <TextField
                fullWidth
                name="taxRate"
                type="number"
                value={formik.values.taxRate}
                onChange={(e) => {
                  const value = e.target.value;
                  // Allow empty string while typing
                  formik.setFieldValue('taxRate', value);
                }}
                onBlur={(e) => {
                  const value = e.target.value;
                  // Convert to number on blur, default to 0 if empty
                  formik.setFieldValue('taxRate', value === '' ? 0 : parseFloat(value));
                  formik.handleBlur(e);
                }}
                onKeyDown={(e) => {
                  if (e.key.toLowerCase() === 'e') {
                    e.preventDefault();
                  }
                }}
                error={!!formik.touched.taxRate && !!formik.errors.taxRate}
                helperText={formik.touched.taxRate && formik.errors.taxRate}
                margin="dense"
                inputProps={{
                  min: 0,
                  max: 100,
                  step: 0.1
                }}
              />
            </Grid>

            <Grid item xs={6}>
              <Typography variant="h6">Description</Typography>
              <TextField
                fullWidth
                name="description"
                value={formik.values.description}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.description && Boolean(formik.errors.description)}
                helperText={formik.touched.description && formik.errors.description}
                margin="dense"
              />
            </Grid>
          </Grid>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button type="submit" variant="contained" color="primary">
              {isEdit ? 'Update' : 'Create'}
            </Button>
          </Box>
        </form>
      </CardContent>
    </Card>
  );
}

export default TaxCategoryCreate;
