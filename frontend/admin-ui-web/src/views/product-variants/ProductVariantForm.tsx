'use client';

import { useState, useEffect, useMemo } from 'react';

// next
import { useRouter, useSearchParams } from 'next/navigation';

// material-ui
import Grid from '@mui/material/Grid';
import TextField from '@mui/material/TextField';
import InputLabel from '@mui/material/InputLabel';

// project-imports
import MainCard from 'components/MainCard';

// assets
import { Autocomplete, Box, CardActions, CircularProgress, FormControlLabel, FormHelperText, Switch } from '@mui/material';
import { useFormik } from 'formik';
import { AutoCompleteOption } from 'types/common';
import {
  useCreateAssetMutation,
  useGetAssetsCountQuery,
  useGetAssetsQuery,
  useUpdateProductVariantByIdMutation
} from 'redux/app/products/productApiSlice';
import { useLazyGetFacetsQuery } from 'redux/app/facet/facetApiSlice';
import { LoadingButton } from '@mui/lab';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { ProductCustomizationForm } from 'views/products/Form/Customization';
import { ProductAsset } from 'views/products/Form/ProductAsset';
import { ProductHighlights } from 'views/products/Form/ProductHighlights';
import { ProductLegalForm } from 'views/products/Form/ProductLegalForm';
import { ProductMetaForm } from 'views/products/Form/ProductMetaForm';
import { PricingAndTax } from './PricingForm';
import { Asset, ProductFacetValue } from 'types/product';
import { Facet } from 'types/facet';
import { ProductVariantUpdateDto } from 'types/product-variant';
import { productVariantSchema } from '../../../validations/product-variant';

interface AssetDto {
  id: string;
  preview: string;
  mimeType: string;
}

// ==============================|| ECOMMERCE - ADD PRODUCT ||============================== //
type Props = {
  initialValue: ProductVariantUpdateDto;
  id: string;
  initialAssets: AssetDto[];
  initialFacets: ProductFacetValue[];
};
function UpdateProductVariant({ initialValue, id, initialAssets, initialFacets }: Props) {
  const router = useRouter();
  const [facetInput, setFacetInput] = useState<string>('');
  const [openGallery, setOpenGallery] = useState<boolean>(false);
  const [selectedAssets, setSelectedAssets] = useState<AssetDto[]>(initialAssets);
  const search = useSearchParams();

  const [getFacets, { isLoading: facetLoading, data: facets }] = useLazyGetFacetsQuery({});
  const [addAsset, { isLoading: isAssetUploading }] = useCreateAssetMutation();
  const [updateVariant, { isLoading: updateLoading }] = useUpdateProductVariantByIdMutation();
  const [page, setPage] = useState(0);
  const [assets, setAssets] = useState<Asset[]>([]);
  const limit = 10;
  const { data: totalAssets } = useGetAssetsCountQuery();
  const { data: paginatedData, refetch: refetchAsset } = useGetAssetsQuery({ order: ['createdOn DESC'], limit, skip: page * limit });

  const fetchFacets = async () => {
    await getFacets({
      where: { name: { ilike: `%${facetInput}%` } },
      include: [
        {
          relation: 'facetValues'
        }
      ]
    }).unwrap();
  };

  const facetOptions: AutoCompleteOption[] = useMemo(() => {
    const formatFacet = (): Facet[] => {
      return initialFacets.map((item) => ({
        ...item.facetValue?.facet,
        id: item.facetValue?.facet?.id ?? '',
        facetValues: [
          {
            id: item.facetValueId,
            createdOn: '',
            name: item.facetValue?.name,
            code: item.facetValue.code,
            facetId: item.facetValue?.facet?.id ?? ''
          }
        ]
      }));
    };

    const values = facets?.length ? facets : formatFacet();

    return (
      values?.flatMap((facet) =>
        facet?.facetValues?.map((fv) => ({
          label: `${facet.name} - ${fv.name}`,
          value: fv.id
        }))
      ) ?? []
    );
  }, [facets, initialFacets]);

  const handleSubmit = async (values: ProductVariantUpdateDto) => {
    const payload: Partial<ProductVariantUpdateDto> = { ...values };

    if (!payload.taxCategoryId) {
      delete payload.taxCategoryId;
    }
    await updateVariant({ id, body: { ...values } }).unwrap();

    openSnackbar({
      open: true,
      message: 'Product variant updated successfully',
      variant: 'alert',
      alert: { color: 'success' }
    } as SnackbarProps);
    router.push(search?.get('callback') ?? '/products');
  };

  const formik = useFormik<ProductVariantUpdateDto>({
    initialValues: initialValue,
    enableReinitialize: true,
    validationSchema: productVariantSchema,
    onSubmit: (values) => {
      const payload: ProductVariantUpdateDto = { ...values };
      handleSubmit(payload);
    }
  });

  const handleFileChange = async (file: File) => {
    if (!file) return;
    const formData = new FormData();
    formData.append('file', file);
    await addAsset(formData).unwrap();
    refetchAsset();
  };

  useEffect(() => {
    if (selectedAssets?.length) {
      const hasFeaturedAsset = selectedAssets.some((asset) => asset.id === formik.values.featuredAssetId);

      if (!hasFeaturedAsset) {
        formik.setFieldValue('featuredAssetId', selectedAssets[0].id);
      }
    } else {
      formik.setFieldValue('featuredAssetId', '');
    }
    formik.setFieldValue(
      'assets',
      selectedAssets.map((asset) => asset.id)
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedAssets]);

  useEffect(() => {
    if (!facetInput) return;

    const debounceTimeout = setTimeout(fetchFacets, 300);
    return () => clearTimeout(debounceTimeout);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [facetInput]);

  useEffect(() => {
    if (paginatedData) {
      setAssets((prev) => [...prev, ...paginatedData]);
    }
  }, [paginatedData]);

  const hasMore = useMemo(() => {
    if (totalAssets?.count) {
      return totalAssets?.count > assets?.length;
    }
    return false;
  }, [assets, totalAssets]);

  const loadMoreAssets = () => {
    if (assets.length < (totalAssets?.count ?? 0)) {
      setPage((prev) => prev + 1);
    }
  };

  useEffect(() => {
    if (!formik.values.assets || formik.values.assets.length === 0) {
      formik.setFieldError('media', 'Media is required');
    }
  }, [formik.values.assets]);

  return (
    <MainCard>
      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={9}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <MainCard>
                  <Grid container spacing={1}>
                    <Grid item xs={12} sm={6} lg={6}>
                      <InputLabel sx={{ mb: 1 }}>Name</InputLabel>
                      <TextField
                        placeholder="Enter name"
                        fullWidth
                        {...formik.getFieldProps('name')}
                        name="name"
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.name && Boolean(formik.errors.name)}
                        helperText={Boolean(formik.errors.name) && formik.touched.name && formik.errors.name}
                        sx={{
                          '& input': {
                            padding: '10px 18px'
                          }
                        }}
                      />
                    </Grid>

                    <Grid item xs={12} sm={6} lg={6}>
                      <InputLabel sx={{ mb: 1 }}>SKU</InputLabel>
                      <TextField
                        fullWidth
                        {...formik.getFieldProps('sku')}
                        name="sku"
                        disabled
                        sx={{
                          '& input': {
                            padding: '10px 18px'
                          }
                        }}
                      />
                    </Grid>
                  </Grid>
                </MainCard>
              </Grid>
              <Grid item xs={12}>
                <ProductAsset
                  assets={assets ?? []}
                  openGallery={openGallery}
                  toggleGallery={() => {
                    setOpenGallery(!openGallery);
                  }}
                  handleFileChange={handleFileChange}
                  isAssetUploading={isAssetUploading}
                  setSelectedAssets={setSelectedAssets}
                  selectedAssets={selectedAssets}
                  handleFeatureAssetChange={(id: string) => {
                    formik.setFieldValue('featuredAssetId', id);
                  }}
                  featuredAssetId={formik.values.featuredAssetId}
                  hasMore={hasMore}
                  loadMoreAssets={loadMoreAssets}
                  assetsCount={totalAssets?.count ?? 0}
                />
              </Grid>
              {formik.touched.assets && formik.errors.assets && (
                <FormHelperText sx={{ ml: 3 }} error>
                  {formik.errors.assets}
                </FormHelperText>
              )}

              <Grid item xs={12}>
                <PricingAndTax formik={formik} />
              </Grid>
              <Grid item xs={12}>
                <ProductCustomizationForm formik={formik} />
              </Grid>
              <Grid item xs={12}>
                <ProductMetaForm formik={formik} />
              </Grid>
              <Grid item xs={12}>
                <ProductHighlights formik={formik} />
              </Grid>

              <Grid item xs={12}>
                <ProductLegalForm formik={formik} />
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={3}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <MainCard title="Product Status">
                  <Box>
                    <FormControlLabel
                      control={<Switch inputProps={{ 'aria-label': 'Enabled switch' }} defaultChecked checked={formik.values.enabled} />}
                      label="Enabled"
                      name="enabled"
                      onChange={() => {
                        formik.setFieldValue('enabled', !formik.values.enabled);
                      }}
                    />
                  </Box>
                </MainCard>
              </Grid>
              <Grid item xs={12}>
                <MainCard title="Facet">
                  <Box>
                    <InputLabel sx={{ mb: 1 }}>Facet</InputLabel>
                    <Autocomplete
                      freeSolo
                      multiple
                      options={facetOptions}
                      getOptionLabel={(option: AutoCompleteOption | string) => (typeof option === 'string' ? option : option.label)}
                      loading={facetLoading}
                      onInputChange={(event, newInputValue) => {
                        setFacetInput(newInputValue);
                      }}
                      value={facetOptions.filter((opt) => formik.values.facets.includes(opt.value))}
                      onChange={(e, newValue: (string | AutoCompleteOption)[]) => {
                        formik.setFieldValue(
                          'facets',
                          newValue.map((item) => (typeof item === 'string' ? item : item.value))
                        );
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Search"
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                              <>
                                {facetLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                {params.InputProps.endAdornment}
                              </>
                            )
                          }}
                          onBlur={formik.handleBlur}
                          error={formik.touched.facets && Boolean(formik.errors.facets)}
                          name="facets"
                        />
                      )}
                    />
                    {Boolean(formik.errors.facets) && formik.touched.facets && formik.errors.facets && (
                      <FormHelperText error>{formik.errors.facets}</FormHelperText>
                    )}
                  </Box>
                </MainCard>
              </Grid>
            </Grid>
          </Grid>
        </Grid>

        <CardActions sx={{ justifyContent: 'flex-end' }}>
          <LoadingButton variant="outlined" color="error" onClick={() => router.back()}>
            Cancel
          </LoadingButton>
          <LoadingButton variant="outlined" type="submit" loading={updateLoading} disabled={updateLoading}>
            Submit
          </LoadingButton>
        </CardActions>
      </form>
    </MainCard>
  );
}
export default UpdateProductVariant;
