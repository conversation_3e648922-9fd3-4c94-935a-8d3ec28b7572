import { Autocomplete, FormHelperText, Grid, InputLabel, TextField, Tooltip } from '@mui/material';
import IconButton from 'components/@extended/IconButton';
import MainCard from 'components/MainCard';
import { DEFAULT_CURRENCY } from 'config';
import { currencyCodeSymbolMap } from 'constants/currency';
import { FormikProps } from 'formik';
import { InfoCircle } from 'iconsax-react';
import { FC, useEffect, useMemo } from 'react';
import { useGetTaxCategoriesQuery } from 'redux/app/tax/taxCategoryApiSlice';
import { AutoCompleteOption } from 'types/common';
import { ProductVariantUpdateDto } from 'types/product-variant';
interface Props {
  formik: FormikProps<ProductVariantUpdateDto>;
}
export const PricingAndTax: FC<Props> = ({ formik }) => {
  const { data: taxCategories, isFetching } = useGetTaxCategoriesQuery();

  const taxCategoryOptions: AutoCompleteOption[] = useMemo(() => {
    return (
      taxCategories
        ?.filter((item): item is { name: string; id: string } => Boolean(item.id))
        .map((item) => ({ label: item.name, value: item.id })) ?? []
    );
  }, [taxCategories]);

  const selectedTaxcategory = useMemo(() => {
    return taxCategories?.find((item) => item.id === formik?.values?.taxCategoryId);
  }, [formik.values.taxCategoryId, taxCategories]);

  useEffect(() => {
    const { priceWithoutTax, taxCategoryId: taxCategory } = formik.values;

    if (priceWithoutTax && taxCategory && taxCategories?.length) {
      const selectedCategory = taxCategories.find((cat) => cat.id === taxCategory);

      if (selectedCategory?.taxRate !== undefined) {
        const taxRate = selectedCategory.taxRate;
        const priceWithTax = Number(priceWithoutTax) + (Number(priceWithoutTax) * taxRate) / 100;

        formik.setFieldValue('price', parseFloat(priceWithTax.toFixed(2)));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.values.priceWithoutTax, formik.values.taxCategoryId, taxCategories]);

  const selectedTaxcategoryOption = useMemo(() => {
    return taxCategoryOptions.find((value) => value.value === formik.values.taxCategoryId) ?? null;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [taxCategoryOptions, formik.values.taxCategoryId, isFetching]);

  return (
    <MainCard title="Price and tax">
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <InputLabel sx={{ mb: 1 }}>Tax Category</InputLabel>
          <Autocomplete
            options={taxCategoryOptions}
            getOptionLabel={(option: AutoCompleteOption | string) => (typeof option === 'string' ? option : option.label)}
            onChange={(e, newValue: AutoCompleteOption | string | null) => {
              formik.setFieldValue('taxCategoryId', typeof newValue === 'string' ? newValue : (newValue?.value ?? ''));
            }}
            value={selectedTaxcategoryOption}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Search"
                InputProps={{
                  ...params.InputProps
                }}
                onBlur={formik.handleBlur}
                error={formik.touched.taxCategoryId && Boolean(formik.errors.taxCategoryId)}
                name="taxCategoryId"
              />
            )}
          />
          {Boolean(formik.errors.taxCategoryId) && formik.touched.taxCategoryId && formik.errors.taxCategoryId && (
            <FormHelperText error>{formik.errors.taxCategoryId}</FormHelperText>
          )}
        </Grid>
        <Grid item xs={12} md={6}>
          <InputLabel sx={{ mb: 1 }}>Price</InputLabel>
          <TextField
            placeholder="Enter unit price"
            fullWidth
            {...formik.getFieldProps('priceWithoutTax')}
            type="number"
            onChange={(event) => {
              const value = event.target.value;
              formik.setFieldValue('priceWithoutTax', value === '' ? '' : Number(value));
            }}
            onBlur={formik.handleBlur}
            error={formik.touched.priceWithoutTax && Boolean(formik.errors.priceWithoutTax)}
            helperText={Boolean(formik.errors.priceWithoutTax) && formik.touched.priceWithoutTax && formik.errors.priceWithoutTax}
          />
        </Grid>

        {selectedTaxcategory && (
          <Grid item xs={12} md={6}>
            <InputLabel sx={{ mb: 1 }}>Price (Incl. {selectedTaxcategory.taxRate}% Tax)</InputLabel>
            <TextField value={`${currencyCodeSymbolMap.get(DEFAULT_CURRENCY)}${formik.values.price}`} fullWidth disabled />
          </Grid>
        )}
        <Grid item xs={12} md={6}>
          <InputLabel sx={{ display: 'flex', alignItems: 'center' }}>
            MRP
            <Tooltip title="MRP can be used to display as an offer in the product listing for customers, helping with marketing." arrow>
              <IconButton size="small" sx={{ ml: 1, padding: 0, color: 'text.secondary' }}>
                <InfoCircle fontSize="small" />
              </IconButton>
            </Tooltip>
          </InputLabel>
          <TextField
            placeholder="Enter mrp"
            fullWidth
            {...formik.getFieldProps('mrp')}
            type="number"
            onChange={(event) => {
              const value = event.target.value;
              formik.setFieldValue('mrp', value === '' ? '' : Number(value));
            }}
            onBlur={formik.handleBlur}
            error={formik.touched.mrp && Boolean(formik.errors.mrp)}
            helperText={formik.touched.mrp && formik.errors.mrp}
          />
        </Grid>
      </Grid>
    </MainCard>
  );
};
