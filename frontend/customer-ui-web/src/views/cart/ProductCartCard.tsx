import {ExpandLess, ExpandMore, Delete} from '@mui/icons-material';
import {
  IconButton,
  Typography,
  Button,
  Collapse,
  FormControlLabel,
  TextField,
  Radio,
  Box,
  Tooltip,
  FormControl,
  RadioGroup,
  FormLabel,
} from '@mui/material';
import {Stack} from '@mui/system';
import MainCard from 'components/MainCard';
import {Add, Edit, Minus} from 'iconsax-react';
import {useEffect, useState} from 'react';
import Image from 'next/image';
import {useUpdateCartItemMutation} from 'redux/ecom/cartApiSlice';
import {ProductVariant} from 'types/product';
import {useRouter} from 'next/navigation';
import ProductCustomizationForm from 'views/product-details/CustomizationForm';
import {useLazyGetProductCustomizationsQuery} from 'redux/ecom/ecomApiSlice';
import {fieldsExcludeMetaFields} from 'types/api';
import {useSet<PERSON>tom} from 'jotai';
import {productCustomizationsAtom} from 'utils/atoms/productCustomizationAtom';

const ProductCartCard = ({
  product,
  index,
  qty,
  refetch,
  cartId,
  minDeliveryDate,
  maxDeliveryDate,
}: {
  product: ProductVariant;
  index: number;
  qty: number;
  cartId?: string;
  refetch: () => void;
  minDeliveryDate?: Date | null;
  maxDeliveryDate?: Date | null;
}) => {
  const setProductCustomizations = useSetAtom(productCustomizationsAtom);
  const [expanded, setExpanded] = useState(false);
  const [giftWrap, setGiftWrap] = useState(true);
  const [giftMessage, setGiftMessage] = useState('');
  const [quantity, setQuantity] = useState(qty);
  const [updateCartItem] = useUpdateCartItemMutation();
  const Personalized = product.productCustomizationFields?.length > 0;

  const [triggerGetCustomizations, {data: customizationFields}] =
    useLazyGetProductCustomizationsQuery();

  // Step 1: Fetch variant-specific customizations
  useEffect(() => {
    if (!product?.id) return;

    const fetchCustomizationFields = async () => {
      let res = await triggerGetCustomizations({
        where: {
          productVariantId: product.id,
        },
        fields: fieldsExcludeMetaFields,
        include: [
          {
            relation: 'productCustomizationOptions',
            scope: {
              fields: fieldsExcludeMetaFields,
            },
          },
          {
            relation: 'customizationValue',
            scope: {
              fields: fieldsExcludeMetaFields,
              where: {
                cartId,
              },
            },
          },
        ],
      }).unwrap();

      // Fallback to productId if res is empty
      if (!res.length && product.product?.id) {
        res = await triggerGetCustomizations({
          where: {
            productId: product.product.id,
          },
          fields: fieldsExcludeMetaFields,
          include: [
            {
              relation: 'productCustomizationOptions',
              scope: {
                fields: fieldsExcludeMetaFields,
              },
            },
            {
              relation: 'customizationValue',
              scope: {
                fields: fieldsExcludeMetaFields,
                where: {
                  cartId,
                },
              },
            },
          ],
        }).unwrap();
      }

      // Now process whichever response we have
      if (Array.isArray(res) && res.length) {
        const customizationValues: Record<string, string | boolean> = {};

        for (const field of res) {
          const value = field.customizationValue?.value;

          if (value !== undefined && value !== null) {
            customizationValues[field.id] =
              field.fieldType === 'checkbox' ? value === 'true' : value;
          }
        }

        setProductCustomizations(prev => ({
          ...prev,
          [product.id]: customizationValues,
        }));
      }
    };

    fetchCustomizationFields();
  }, [product, triggerGetCustomizations]);

  const router = useRouter();

  // const handleNavigateToProduct = () => {
  //   router.push(`/product-details/${product.id}`);
  // };

  const handleNavigateToProduct = () => {
    window.open(`/product-details/${product.id}`, '_blank'); // opens in new tab
  };

  const handleIncrease = () => {
    handleQuantityUpdate(quantity + 1);
  };

  const handleDecrease = () => {
    if (quantity > 1) {
      handleQuantityUpdate(quantity - 1);
    }
  };

  const handleQuantityUpdate = async (newQuantity: number) => {
    setQuantity(newQuantity);
    await updateCartItem({
      productVariantId: product.id,
      quantity: newQuantity,
    }).unwrap();
    refetch();
  };

  const handleManualChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value >= 1) {
      handleQuantityUpdate(value);
    }
  };

  const handleRemove = async () => {
    await updateCartItem({
      productVariantId: product.id,
      quantity: 0,
    }).unwrap();
    refetch();
  };

  return (
    <MainCard
      sx={{
        p: 2,
        mb: 2,
        borderRadius: 2,
        boxShadow: 1,
        position: 'relative',
      }}
    >
      <IconButton
        sx={{position: 'absolute', top: 8, right: 8}}
        onClick={() => setExpanded(!expanded)}
      >
        {expanded ? <ExpandLess /> : <ExpandMore />}
      </IconButton>

      <Stack direction="row" spacing={10}>
        <Box
          onClick={handleNavigateToProduct}
          key={index}
          sx={{
            width: 150,
            height: 150,
            flexShrink: 0,
            borderRadius: 2,
            overflow: 'hidden',
            position: 'relative',
            cursor: 'pointer',
          }}
        >
          <Image
            src={product.featuredAsset?.previewUrl ?? ''}
            alt={`Thumbnail ${index}`}
            fill
            style={{objectFit: 'contain'}}
          />
        </Box>

        <Stack spacing={1} flexGrow={1}>
          <Typography
            onClick={handleNavigateToProduct}
            sx={{cursor: 'pointer'}}
            variant="h6"
            fontWeight="bold"
          >
            {product.name}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {product.product?.description ?? ''}
          </Typography>
          <Stack direction="row" spacing={1} alignItems="center">
            <Typography variant="h6" fontWeight="bold">
              ₹{product.productVariantPrice.price}
            </Typography>
            {product.productVariantPrice.price <
              product.productVariantPrice.mrp && (
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{textDecoration: 'line-through'}}
              >
                M.R.P.: ₹{product.productVariantPrice.mrp}
              </Typography>
            )}
          </Stack>
          {/* Delivery Estimate */}
          {minDeliveryDate && maxDeliveryDate && (
            <Typography
              variant="body2"
              color="text.secondary"
              fontWeight="bold"
            >
              Estimated Delivery between:{' '}
              {new Intl.DateTimeFormat('en-US', {
                month: 'short',
                day: 'numeric',
              }).format(new Date(minDeliveryDate))}{' '}
              –{' '}
              {new Intl.DateTimeFormat('en-US', {
                month: 'short',
                day: 'numeric',
              }).format(new Date(maxDeliveryDate))}
            </Typography>
          )}
          {/* Quantity section */}
          <Stack direction="row" alignItems="center" spacing={1}>
            <TextField
              value={quantity}
              size="small"
              inputProps={{min: 1, style: {textAlign: 'center'}}}
              sx={{
                width: 150,
                '& input': {p: 1, textAlign: 'center'},
              }}
              onChange={handleManualChange}
              InputProps={{
                startAdornment: (
                  <IconButton
                    size="small"
                    onClick={quantity === 1 ? handleRemove : handleDecrease}
                  >
                    {quantity === 1 ? (
                      <Delete fontSize="small" color="error" />
                    ) : (
                      <Minus fontSize="small" />
                    )}
                  </IconButton>
                ),
                endAdornment: (
                  <IconButton size="small" onClick={handleIncrease}>
                    <Add fontSize="small" />
                  </IconButton>
                ),
              }}
            />
            {Personalized ? ( // Replace with your actual condition
              <Tooltip title="Personalized">
                <IconButton
                  onClick={() => router.push(`/product-details/${product.id}`)}
                  sx={{
                    p: 0.5,
                    mb: 0.5,
                    borderRadius: '50%',
                    bgcolor: '#cfd8dc',
                    color: '#000',
                    '&:hover': {
                      bgcolor: '#b0bec5',
                    },
                  }}
                >
                  <Edit fontSize="small" />
                </IconButton>
              </Tooltip>
            ) : (
              <Box sx={{height: 32, mb: 0.5}} />
            )}
          </Stack>
        </Stack>
      </Stack>

      <Collapse in={expanded}>
        <Box sx={{mt: 2}}>
          {product.product?.isGiftWrapAvailable && (
            <FormControl component="fieldset" sx={{width: '100%'}}>
              <FormLabel sx={{fontWeight: 'bold', color: '#00004F', mb: 1}}>
                Gift wrap and Gift message confirmation
              </FormLabel>

              <Box
                display="flex"
                alignItems="center"
                justifyContent="space-between"
              >
                {/* Radio buttons */}
                <RadioGroup
                  row
                  value={giftWrap ? 'yes' : 'no'}
                  onChange={e => setGiftWrap(e.target.value === 'yes')}
                >
                  <FormControlLabel
                    value="yes"
                    control={
                      <Radio
                        sx={{
                          color: '#00004F',
                          '&.Mui-checked': {color: '#00004F'},
                        }}
                      />
                    }
                    label="Yes"
                  />
                  <FormControlLabel
                    value="no"
                    control={
                      <Radio
                        sx={{
                          color: '#00004F',
                          '&.Mui-checked': {color: '#00004F'},
                        }}
                      />
                    }
                    label="No"
                  />
                </RadioGroup>

                {/* Price - only when Yes is selected */}
                {giftWrap && (
                  <Typography
                    variant="body1"
                    sx={{fontWeight: 'bold', color: '#00004F', mr: 2}}
                  >
                    ₹{product.product?.isGiftWrapCharge || 0}
                  </Typography>
                )}
              </Box>
            </FormControl>
          )}

          {giftWrap && (
            <TextField
              multiline
              rows={3}
              placeholder="Gift message..."
              fullWidth
              value={giftMessage}
              onChange={e => setGiftMessage(e.target.value)}
              sx={{
                mt: 2,
                '& .MuiOutlinedInput-root': {
                  borderRadius: '1rem',
                },
              }}
            />
          )}
        </Box>

        {/* Customized Section */}
        {Array.isArray(customizationFields) &&
          customizationFields.length > 0 && (
            <Box
              sx={{
                mt: 3,
                p: 2,
                bgcolor: '#f3f4f6',
                borderRadius: 3,
              }}
            >
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                color="#00004F"
                sx={{mb: 1}}
              >
                Customized
              </Typography>

              <ProductCustomizationForm
                fields={customizationFields || []}
                productVariantId={product.id}
                cartId={cartId}
                isInCart={true}
              />
            </Box>
          )}
        <Stack direction="row" spacing={2} mt={3} justifyContent="center">
          <Button
            variant="contained"
            sx={{
              width: '140px',
              borderRadius: '20px',
              bgcolor: '#00004F',
              fontWeight: 'bold',
              py: 0.8,
              fontSize: '0.75rem',
              minHeight: '32px',
              '&:hover': {
                bgcolor: '#00004F',
              },
            }}
          >
            Save for later
          </Button>

          <Button
            variant="contained"
            color="error"
            startIcon={<Delete fontSize="small" />}
            onClick={handleRemove}
            sx={{
              width: '140px',
              borderRadius: '20px',
              bgcolor: '#A4A4A4',
              fontWeight: 'bold',
              py: 0.8,
              fontSize: '0.75rem',
              minHeight: '32px',
              '&:hover': {
                bgcolor: '#A4A4A4',
              },
            }}
          >
            Remove
          </Button>
        </Stack>
      </Collapse>
    </MainCard>
  );
};

export default ProductCartCard;
