import React, {useEffect, useMemo} from 'react';
import {Box, Card, CardMedia, Grid, Typography} from '@mui/material';
import {useParams} from 'next/navigation';
import {useGetPinnedProductsQuery} from 'redux/pinned/pinnedProductApiSlice';
import {IFilter} from 'types/filter';
import {useLazyGetProductVariantsQuery} from 'redux/ecom/ecomApiSlice';
import {ProductVariant} from 'types/product';

const PinnedProducts: React.FC = () => {
  const params = useParams();
  const productSellerId =
    typeof params?.id === 'string'
      ? params.id
      : Array.isArray(params?.id)
        ? params.id[0]
        : '';

  const {
    data: pinnedProductsData,
    isLoading: pinnedLoading,
    isError: pinnedError,
  } = useGetPinnedProductsQuery(
    {where: {sellerId: productSellerId}},
    {skip: !productSellerId},
  );

  const pinnedVariantIds = useMemo(() => {
    return (
      pinnedProductsData
        ?.filter(item => item)
        .map(p => p.productVariantId)
        .filter(Boolean) ?? []
    );
  }, [pinnedProductsData]);

  const filter: IFilter = useMemo(() => {
    return {
      where: {
        id: {inq: pinnedVariantIds.length > 0 ? pinnedVariantIds : ['']},
      },
      include: [
        {
          relation: 'product',
          scope: {
            where: {sellerId: productSellerId},
            include: [{relation: 'featuredAsset'}, {relation: 'collection'}],
          },
        },
        {relation: 'product'},
        {relation: 'featuredAsset'},
      ],
    };
  }, [pinnedVariantIds, productSellerId]);

  const [triggerGetProductVariants, {data: products, isLoading, isError}] =
    useLazyGetProductVariantsQuery();

  useEffect(() => {
    if (pinnedVariantIds.length === 0) return;

    triggerGetProductVariants(filter);
  }, [filter, pinnedVariantIds.length, triggerGetProductVariants]); // Added dependencies for useEffect

  const filteredVariants = useMemo(() => {
    return (
      products?.filter(
        pv => pv.product && pv.product.sellerId === productSellerId,
      ) || []
    );
  }, [products, productSellerId]);

  if (pinnedLoading) {
    return <Typography sx={{m: 4}}>Loading pinned products...</Typography>;
  }

  if (pinnedError) {
    return (
      <Typography sx={{m: 4, color: 'error.main'}}>
        Failed to load pinned products.
      </Typography>
    );
  }

  if (!pinnedProductsData || pinnedProductsData.length === 0) {
    return <Typography sx={{m: 4}}>No pinned products found.</Typography>;
  }

  if (isLoading) {
    return <Typography sx={{m: 4}}>Loading product variants...</Typography>;
  }

  if (isError) {
    return (
      <Typography sx={{m: 4, color: 'error.main'}}>
        Failed to load product variants.
      </Typography>
    );
  }

  return (
    <Box sx={{flexGrow: 1, p: 2}}>
      <Typography variant="h3" component="h2" gutterBottom sx={{mb: 3, ml: 4}}>
        Pinned Products
      </Typography>
      <Grid container spacing={2}>
        {filteredVariants.length > 0 ? (
          filteredVariants.map((product: ProductVariant) => {
            const image =
              product.featuredAsset?.previewUrl ||
              product.featuredAsset?.preview ||
              product.product?.featuredAsset?.previewUrl ||
              product.product?.featuredAsset?.preview ||
              '';

            const description =
              product.product?.description || 'No description';

            const category =
              (product.product as any)?.collection?.name ||
              product.product?.collection ||
              'No category';

            const productVariantId = product.id;

            const handleCardClick = () => {
              if (productVariantId) {
                window.open(`/product-details/${productVariantId}`, '_blank');
              }
            };

            return (
              <Grid item xs={12} sm={6} md={4} key={product.id}>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                  }}
                >
                  <Card
                    onClick={handleCardClick}
                    sx={{
                      width: '100%',
                      maxWidth: 380,
                      height: 300,
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      mt: 2,
                      boxShadow: 'none',
                      borderRadius: '8px',
                      overflow: 'hidden',
                      cursor: 'pointer',
                    }}
                  >
                    {image ? (
                      <CardMedia
                        component="img"
                        image={image}
                        alt={description}
                        sx={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'contain',
                        }}
                      />
                    ) : (
                      <Box
                        sx={{
                          width: '100%',
                          height: '100%',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          bgcolor: 'grey.300',
                        }}
                      >
                        <Typography>No Image</Typography>
                      </Box>
                    )}
                  </Card>
                  <Typography
                    variant="h5"
                    color="text.primary"
                    sx={{mt: 2, alignSelf: 'center'}}
                  >
                    {category}
                  </Typography>
                </Box>
              </Grid>
            );
          })
        ) : (
          <Typography variant="body1" sx={{m: 4}}>
            No pinned products available.
          </Typography>
        )}
      </Grid>
    </Box>
  );
};

export default PinnedProducts;
