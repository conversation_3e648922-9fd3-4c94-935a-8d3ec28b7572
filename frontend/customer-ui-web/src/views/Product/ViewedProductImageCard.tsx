'use client';
import Image from 'next/image';
import {CURRENCY_MAP} from 'constants/product';
import {Tooltip} from '@mui/material';
import {useAddItemToCartMutation} from 'redux/ecom/cartApiSlice';
import {CartItem} from 'types/cart';
import {openSnackbar} from 'api/snackbar';
import {SnackbarProps} from 'types/snackbar';
import {useAppSelector} from 'redux/hooks';
import {
  useAddItemToWishlistMutation,
  useRemoveItemFromWishlistMutation,
} from 'redux/ecom/wishlistApiSlice';
import React, {useState} from 'react';
import {
  CardMedia,
  Typography,
  IconButton,
  Box,
  Rating,
  Grid,
  Button,
  Stack,
} from '@mui/material';
import FavoriteIcon from '@mui/icons-material/Favorite';
import {ProductVariant} from 'types/product';
import {useRouter} from 'next/navigation';
import {Edit} from 'iconsax-react';

const ViewedProductCard: React.FC<{productVariant: ProductVariant}> = ({
  productVariant,
}) => {
  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);
  const [addItemToCart, {isLoading: isAddingToCart}] =
    useAddItemToCartMutation();
  const [isAddedToCart, setIsAddedToCart] = useState(false);

  const router = useRouter();

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.stopPropagation();

    const cartItem: Partial<CartItem> = {
      productVariantId: productVariant.id,
      quantity: 1,
    };

    await addItemToCart(cartItem).unwrap();
    setIsAddedToCart(true);
    openSnackbar({
      open: true,
      message: 'Product added to Cart',
      variant: 'alert',
      alert: {color: 'success'},
    } as SnackbarProps);
  };

  const handleGoToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push('/cart');
  };

  const [isWishlisted, setIsWishlisted] = useState(
    productVariant.wishlist?.id ? true : false,
  );
  const [addItemToWishlist, {isLoading: isAddingToWishlist}] =
    useAddItemToWishlistMutation();
  const [removeItemFromWishlist, {isLoading: isRemovingFromWishlist}] =
    useRemoveItemFromWishlistMutation();
  const [wishlistId, setWishlistId] = useState<string | undefined>(
    productVariant.wishlist?.id,
  );

  const handleToggleWishlist = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }
    if (isWishlisted && wishlistId) {
      await removeItemFromWishlist(wishlistId).unwrap();
      openSnackbar({
        open: true,
        message: 'Removed from wishlist',
        variant: 'alert',
        alert: {color: 'info'},
      } as SnackbarProps);
      setIsWishlisted(false);
      setWishlistId(undefined);
    } else {
      const result = await addItemToWishlist(productVariant.id).unwrap();
      openSnackbar({
        open: true,
        message: 'Added to wishlist',
        variant: 'alert',
        alert: {color: 'success'},
      } as SnackbarProps);
      setIsWishlisted(true);
      setWishlistId(result.id);
    }
  };

  const totalRatings = productVariant.reviews?.length ?? 0;
  const avgRating =
    totalRatings > 0
      ? productVariant.reviews!.reduce((sum, r) => sum + r.rating, 0) /
        totalRatings
      : 0;

  const price = productVariant.productVariantPrice?.price;
  const mrp = productVariant.productVariantPrice?.mrp;
  const currency =
    CURRENCY_MAP[productVariant.productVariantPrice?.currencyCode] ?? '₹';

  const parsedPrice = parseFloat(price ?? '0');
  const parsedMrp = parseFloat(mrp ?? '0');
  const isDiscountValid =
    !isNaN(parsedPrice) && !isNaN(parsedMrp) && parsedPrice < parsedMrp;
  const discountPercent = `${Math.round(
    (1 - parsedPrice / parsedMrp) * 100,
  )}% off`;

  const Personalized = productVariant.productCustomizationFields?.length > 0;

  return (
    <Grid container spacing={1} sx={{p: 1}}>
      <Grid item xs={12}>
        <Box sx={{position: 'relative'}}>
          <CardMedia
            onClick={() =>
              window.open(`/product-details/${productVariant.id}`, '_blank')
            }
            component="img"
            height="200"
            image={productVariant.featuredAsset?.previewUrl}
            alt={productVariant.name}
            sx={{
              width: '100%',
              height: '300px',
              borderRadius: '20px',
              padding: '10px',
              backgroundSize: 'contain',
              objectFit: 'fill',
            }}
          />
          <IconButton
            onClick={handleToggleWishlist}
            disabled={isAddingToWishlist || isRemovingFromWishlist}
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
              backgroundColor: '#fff',
              borderRadius: '50%',
              boxShadow: 1,
              zIndex: 2,
              color: isWishlisted ? '#9A2D8E' : 'inherit',
            }}
          >
            {isWishlisted ? (
              <FavoriteIcon sx={{fontSize: 32, color: '#FF0000 !important'}} />
            ) : (
              <Image
                src="/assets/images/wishlist.svg"
                alt="Similar Icon"
                width={20}
                height={20}
                style={{fill: 'red'}}
              />
            )}
          </IconButton>
          <IconButton
            aria-label="similar"
            sx={{
              position: 'absolute',
              bottom: '13px',
              right: '13px',
              backgroundColor: 'rgba(255, 255, 255, 1)',
              borderRadius: '50%',
              width: 28,
              height: 28,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 1)',
              },
              zIndex: 2,
            }}
          >
            <Image
              src="/assets/images/similar.svg"
              alt="Similar Icon"
              width={20}
              height={20}
              style={{fill: 'red'}}
            />
          </IconButton>
        </Box>
      </Grid>
      <Grid container spacing={4} sx={{flexGrow: 1, p: 2, mt: -4}}>
        <Grid item xs={7}>
          <Typography
            variant="subtitle1"
            fontWeight={600}
            sx={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {productVariant.name}
          </Typography>

          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              mb: 0.5,
            }}
          >
            {productVariant.product?.description || ''}
          </Typography>

          <Stack direction="row" spacing={0.5} alignItems="center" sx={{mb: 1}}>
            <Rating
              name="read-only"
              value={parseFloat(avgRating.toFixed(1))}
              precision={0.1}
              readOnly
              size="small"
              sx={{
                '& .MuiRating-iconFilled': {color: '#f0c14b'},
                '& .MuiRating-iconEmpty': {color: '#ccc'},
              }}
            />
            <Typography variant="caption" color="text.secondary">
              ({totalRatings})
            </Typography>
          </Stack>

          {isAddedToCart ? (
            <Button
              onClick={handleGoToCart}
              sx={{
                minWidth: '6.5rem',
                borderRadius: '1rem',
                color: 'white',
                backgroundColor: 'primary.main',
                fontSize: {xs: '0.7rem', sm: '0.8rem'},
                '&:hover': {
                  backgroundColor: 'primary.dark',
                  color: 'white',
                },
              }}
            >
              Go to Cart
            </Button>
          ) : (
            <Button
              onClick={handleAddToCart}
              disabled={isAddingToCart}
              sx={{
                minWidth: '6.5rem',
                borderRadius: '1rem',
                color: 'black',
                backgroundImage:
                  'linear-gradient(to right,#9A2D8E 50%, white 50%)',
                backgroundSize: '200% 100%',
                backgroundPosition: 'right bottom',
                transition:
                  'background-position 0.4s ease-in-out, color 0.4s ease-in-out',
                border: '0.5px solid black',
                fontSize: {xs: '0.7rem', sm: '0.8rem'},

                '&:hover': {
                  backgroundPosition: 'left bottom',
                  color: 'white',
                },
              }}
            >
              {isAddingToCart ? 'Adding...' : 'Add to Cart'}
            </Button>
          )}
        </Grid>

        <Grid item xs={5} sx={{textAlign: 'right'}}>
          {Personalized ? (
            <Tooltip title="Personalized">
              <IconButton
                onClick={() =>
                  window.open(`/product-details/${productVariant.id}`, '_blank')
                }
                sx={{
                  p: 0.25,
                  mb: 0.5,
                  borderRadius: '50%',
                  bgcolor: '#cfd8dc',
                  color: '#000',
                  width: 28,
                  height: 28,
                  '&:hover': {
                    bgcolor: '#b0bec5',
                  },
                }}
              >
                <Edit />
              </IconButton>
            </Tooltip>
          ) : (
            <Box sx={{height: 32, mb: 0.5}} />
          )}

          {isDiscountValid && (
            <Typography
              variant="body2"
              sx={{color: '#9A2D8E', fontWeight: 700, mb: 0.5}}
            >
              Deal: {discountPercent}
            </Typography>
          )}
          {isDiscountValid && (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                textDecoration: 'line-through',
                fontSize: '0.60rem',
                mb: 0.5,
              }}
            >
              M.R.P.: {currency} {mrp}
            </Typography>
          )}

          <Typography variant="h6" fontWeight="bold">
            {currency} {price}
          </Typography>
        </Grid>
      </Grid>
    </Grid>
  );
};

export default ViewedProductCard;
