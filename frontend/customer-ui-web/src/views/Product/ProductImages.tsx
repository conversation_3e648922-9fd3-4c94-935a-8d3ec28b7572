'use client';

import React from 'react';
import {CardMedia, Box, Grid} from '@mui/material';
import {ProductVariant} from 'types/product';

const ProductImages: React.FC<{productVariant: ProductVariant}> = ({
  productVariant,
}) => {
  return (
    <Grid spacing={2} justifyContent="flex-start">
      <Box sx={{position: 'relative'}}>
        <CardMedia
          onClick={() =>
            window.open(`/product-details/${productVariant.id}`, '_blank')
          }
          component="img"
          height="200"
          image={productVariant.featuredAsset?.previewUrl}
          alt={productVariant.name}
          sx={{
            width: '180px',
            height: '180px',
            borderRadius: '20px',
            padding: '10px',
            backgroundSize: 'contain',
            objectFit: 'fill',
          }}
        />
      </Box>
    </Grid>
  );
};

export default ProductImages;
